import React, { useState, useContext, useEffect, use } from 'react'
import { AuthenticatedTemplate, UnauthenticatedTemplate, useMsal } from '@azure/msal-react'
import Image from 'next/image'
import styles from './Navbar.module.css'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import ClickAwayListener from '../Dropdown/ClickAwayListener'
import AppLogo from '../../../public/LoginScreen/Do_creative .svg'
import WorkDay from '../../../public/images/ModuleLauncherImages/Workday.png'
import Canvas from '../../../public/images/ModuleLauncherImages/Canvas.png'
import PeopleSoft from '../../../public/images/ModuleLauncherImages/PeopleSoft.png'
import Performance from '../../../svg/Performance Review_main icon.svg'
import Project from '../../../public/images/ModuleLauncherImages/Project.png'
import Vendor from '../../../public/images/ModuleLauncherImages/Vendor.png'
import Onboarding from '../../../svg/WorkflowCanvas/Suplier.svg'
import Purchase from '../../../public/images/ModuleLauncherImages/Purchase.svg'
import HelpDesk from '../../../public/images/ModuleLauncherImages/HelpDesk.png'
import PaymentCard from '../../../public/images/ModuleLauncherImages/PaymentCard.png'
import JobRequisition from '../../../public/images/ModuleLauncherImages/JobRequisition.png'
import ITAccess from '../../../public/images/ModuleLauncherImages/ITAccess.png'
import CRM from '../../../svg/metronic/lead_logo.svg'
import LMS from '../../../public/svg/LMS/LMS Logo.svg'
import ContractManagement from '../../../public/images/ModuleLauncherImages/ContractManagement.png'
import { useRouter } from 'next/router'
import ServiceNow from '../../../public/images/ModuleLauncherImages/ServiceNow.png'
import policiesProcedures from '../../../public/svg/Common/Policies & Procedures Library.svg'
import { UserImageContext } from '../../../public/ContextProviders/UserImageContextProvider'
import Messages from '../../../svg/MyMessages_header.svg'
import Notification from '../../../svg/Notification.svg'
import leadNoti from '../../../svg/metronic/notification_nav.svg'
import leadSettings from '../../../svg/metronic/setting_nav.svg'
import leadMessage from '../../../svg/metronic/lead_message.svg'
import Settings from '../../../svg/Setting_header.svg'
import AppMenu from '../../../svg/App_menu.svg'
import useUtilityFunctions from '../../../hooks/useUtilityFunctions'
import { ConditionalDisplay } from '../ConditionalDisplay/ConditionalDisplay'
import { LayoutContext } from '../Layouts/LayoutContextProvider'
import { UserProfileDisplayNameLabel, UserProfileImage, UserProfileInfoLabel } from '../UserProfileImage/UserProfileImage'
import PrimaryButton from '../PrimaryButton/PrimaryButton'
import SecondaryButton from '../SecondaryButton/SecondaryButton'
import { useClickAwayListener } from '../../../hooks/useClickAwayListener'
import { MicrosoftEntraIdContext } from '../../../public/ContextProviders/MicrosoftEntraIdContextProvider'
import { NotificationDropdown } from '../NotificationDropdown/NotificationDropdown'
import SignalRContext from '../../../public/SignalRContext/SignalRContext'
import DocumentSearchDropdown from '../DocumentSearchDropdown/DocumentSearchDropdown'
import TextInput from '../Input/TextInput/TextInput'

export default function Navbar({ router, solutionId = 1 }) {
  const layoutContext = useContext(LayoutContext)
  const imageName = process.env.NEXT_PUBLIC_IMAGE_NAV
  const navLogo = require(`../../../public/LoginScreen/${imageName}`)
  const { instance, accounts } = useMsal()
  const [popupModule, setPopupModule] = useState(false)
  const userProfile = useContext(UserProfileContext)
  const { connection } = useContext(SignalRContext)

  // Until we get the user role it is assigned as End User, when we are waiting for the api call to complete we are assigning it to empty string
  const account = accounts.length ? accounts[0] : {}
  const { name } = account
  const currentPath = router.pathname

  const userRole = userProfile?.role?.name
  const userEmail = userProfile?.email
  const userId = userProfile?.userID

  const [messageCount, setMessageCount] = useState(0)

  console.log('layer', layoutContext)

  useEffect(() => {
    if (userEmail && connection) {
      if (connection.isConnected()) {
        const handler = (unreadMessagesCount) => {
          setMessageCount(unreadMessagesCount)
        }

        // Remove existing listener to prevent duplicates
        connection.off('allunreadmessagecount')
        connection.onAllUnreadMessageCount(handler)

        // Use userEmail instead of userId as per SignalR connection implementation
        connection.messagesCount(userEmail)
      } else {
        connection.reconnect()
      }

      // Cleanup: Remove listener when component unmounts or dependencies change
      return () => {
        connection.off('allunreadmessagecount')
      }
    }
  }, [userEmail, connection])

  const getPathBasedOnRole = (role) => {
    const rolePaths = {
      Contributor: '/ApprovalPage',
      Admin: '/ApprovalPage',
      Viewer: '/MySubmissions',
      Creator: '/MySubmissions',
      Designer: '/MySubmissions'
    }

    return rolePaths[role] || null
  }

  const isMetronicPath = router.pathname.includes('LeadGeneration') || router.pathname.includes('LMS')

  const features = [
    {
      name: process.env.NEXT_PUBLIC_APP_NAME,
      logo: navLogo,
      width: 100,
      height: 30,
      path: getPathBasedOnRole(userRole),
      backgroundColor: '#f1fbff'
    },
    {
      name: 'Canvas',
      logo: Canvas,
      width: 100,
      height: 30,
      backgroundColor: '#fffcf2'
    },
    {
      name: 'Workday',
      logo: WorkDay,
      width: 85,
      height: 30,
      backgroundColor: '#f8f9fb'
    },
    {
      name: 'PeopleSoft',
      logo: PeopleSoft,
      width: 85,
      height: 20,
      backgroundColor: '#f8f9fb'
    },
    {
      name: 'CRM',
      logo: CRM,
      width: 40,
      height: 40,
      path: '/LeadGeneration',
      backgroundColor: '#f1fbff',
      solutionId: 11
    },

    ...(userEmail === '<EMAIL>' || '<EMAIL>'
      ? [
          {
            // name: 'LMS',
            logo: LMS,
            width: 73,
            height: 34,
            path: userEmail === '<EMAIL>' ? '/LMS' : '/InstructorLMS',
            backgroundColor: '#f1fbff',
            solutionId: 12
          }
        ]
      : []),
    {
      name: 'Onboarding Process',
      logo: Project,
      width: 40,
      height: 40,
      path: '/OnBoarding',
      backgroundColor: '#fffcf2',
      solutionId: 3
    },
    {
      name: 'Supplier Qualification',
      logo: Onboarding,
      width: 40,
      height: 40,
      path: '/Supplier',
      backgroundColor: '#f1fbff'
    },
    {
      name: 'Project Requisition',
      logo: Vendor,
      width: 40,
      height: 40,
      path: '/ProjectRequisition',
      backgroundColor: '#fffcf2',
      solutionId: 5
    },
    {
      name: 'Purchase Requisition',
      logo: Purchase,
      width: 40,
      height: 40,
      path: '/PurchaseOrder',
      backgroundColor: '#f8f9fb',
      solutionId: 6
    },
    {
      name: 'Help Desk',
      logo: HelpDesk,
      width: 40,
      height: 40,
      path: '/MyTickets',
      backgroundColor: '#f8f9fb'
    },
    {
      name: 'Job Requisition',
      logo: JobRequisition,
      width: 40,
      height: 40,
      path: '/JobRequestion',
      backgroundColor: '#f1fbff'
    },
    {
      name: 'ServiceNow',
      logo: ServiceNow,
      width: 85,
      height: 20,
      backgroundColor: '#fffcf2'
    },
    {
      name: 'Access Request',
      logo: ITAccess,
      width: 40,
      height: 40,
      path: '/AccessRequest',
      backgroundColor: '#f1fbff',
      solutionId: 7
    },
    {
      name: 'Payment Card',
      logo: PaymentCard,
      width: 40,
      height: 40,
      path: '/view/dad3b8dd-876d-4462-88c4-46ea9532924b',
      backgroundColor: '#fffcf2'
    },
    {
      name: 'Supplier & Contract Legal Management',
      logo: ContractManagement,
      width: 40,
      height: 40,
      path: '/Contracts',
      backgroundColor: '#f8f9fb'
    },
    {
      name: 'Policies & Procedures',
      logo: policiesProcedures,
      width: 40,
      height: 40,
      path: '/Poilcy-and-procedures',
      backgroundColor: '#f1fbff',
      solutionId: 9
    },
    {
      name: 'Asset Tracker',
      logo: ITAccess,
      width: 40,
      height: 40,
      path: '/AssetTracker',
      backgroundColor: '#f1fbff',
      solutionId: 10
    },
    {
      name: 'Performance Review',
      logo: Performance,
      width: 40,
      height: 40,
      path: '/PerformanceReview',
      backgroundColor: '#f1fbff',
      solutionId: 2
    }
  ]

  const findLogoDetailsForCurrentPath = () => {
    const feature = features.find((f) => {
      if (f?.path === undefined) {
        console.log('feature', currentPath, f.path, currentPath.includes(f.path) || f?.solutionId === layoutContext.solutionId)
      }

      return currentPath.includes(f.path) || f?.solutionId === solutionId
    })

    if (currentPath === '/ApprovalPage' || currentPath === '/MySubmissions' || currentPath === '/') {
      return { logo: navLogo, width: 140, height: 45, name: '' }
    }

    if (feature) {
      return {
        logo: feature.logo,
        width: feature.width,
        height: feature.height,
        name: feature.name
      }
    }

    return { logo: navLogo, width: 140, height: 45, name: '' }
  }

  const { logo: dynamicLogo, width, height, name: featureName } = findLogoDetailsForCurrentPath()

  return (
    <>
      <AuthenticatedTemplate>
        <div style={{ position: 'relative' }}>
          <div className={isMetronicPath ? styles.metronicContainer : styles.containerBackground}>
            <Logo
              setPopupModule={setPopupModule}
              dynamicLogo={dynamicLogo}
              width={width}
              height={height}
              featureName={featureName}
              popupModule={popupModule}
              features={features}
            />
            <Profile name={name} userRole={userRole} messageCount={messageCount} isMetronicPath={isMetronicPath} solutionId={solutionId} />
          </div>
        </div>
      </AuthenticatedTemplate>
      <UnauthenticatedTemplate>
        <div style={{ position: 'relative' }}>
          <div className={styles.containerBackground}>
            <Logo />
            <Profile solutionId={solutionId} />
          </div>
        </div>
      </UnauthenticatedTemplate>
    </>
  )
}

const Profile = ({ name, userRole, messageCount, isMetronicPath, solutionId }) => {
  const router = useRouter()
  const layoutContext = useContext(LayoutContext)

  const [showProfileModal, setShowProfileModal] = useState(false)
  const [showNotificationDropdown, setShowNotificationDropdown] = useState(false)

  const userProfile = useContext(UserProfileContext)

  const jobTitle = userProfile?.jobTitle ?? '-'
  const roleSubtitle = userProfile && Object.keys(userProfile).length > 0 ? (userProfile?.role && userProfile.role.name) ?? 'End User' : ''

  const roleName =
    Array.isArray(userProfile?.roleNames) && userProfile?.roleNames.length > 0
      ? userProfile?.roleNames?.join(' | ')
      : userProfile?.isGlobalAdmin
      ? 'Admin'
      : 'End User'

  const handleUserProfileImageClick = (e) => {
    e.stopPropagation()
    setShowProfileModal((prev) => !prev)
  }

  const handleNotificationClick = (e) => {
    e.stopPropagation()
    setShowNotificationDropdown((prev) => !prev)
  }

  const { ref: notificationDropdownRef } = useClickAwayListener({
    onOutsideClick: () => setShowNotificationDropdown(false)
  })

  const notificationIcon = isMetronicPath ? leadNoti : Notification
  const settingsIcon = isMetronicPath ? leadSettings : Settings
  const messageIcon = isMetronicPath ? leadMessage : Messages

  const isMetronic = layoutContext.solutionId === 12 || layoutContext.solutionId === 11 || layoutContext.solutionId === 13

  return (
    <div className={styles.profileContainer}>
      {!isMetronic && <DocumentSearchDropdown metronic={isMetronicPath} />}

      <div className={styles.inboxWrapper}>
        <Image
          src={messageIcon}
          alt="Messages"
          className={styles.myMessageHeader}
          style={{ width: '20px', cursor: 'pointer' }}
          onClick={() =>
            router.push({
              pathname: '/Inbox',
              query: {
                userID: userProfile.userID
              }
            })
          }
        />
        {messageCount > 0 && (
          <div className={styles.countHead}>
            <span className={styles.countBack} style={{ width: messageCount < 10 ? '1.5rem' : '1.75rem' }}>
              {messageCount < 100 ? messageCount : '99+'}
            </span>
          </div>
        )}
      </div>
      <Image src={notificationIcon} alt="Notification" className={styles.navbarIcon} onClick={handleNotificationClick} />
      <ConditionalDisplay condition={showNotificationDropdown}>
        <div ref={notificationDropdownRef} className={styles.dropdownContainer}>
          <NotificationDropdown />
        </div>
      </ConditionalDisplay>
      <Image src={settingsIcon} alt="Settings" style={{ width: '20px' }} />
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {!isMetronic && (
          <span
            style={{
              display: 'flex',
              flexDirection: 'column',
              marginRight: '0.5rem'
            }}
          >
            <span style={{ fontSize: '1rem', fontWeight: 'bold' }}>{name}</span>
            {/* <small className={styles.roleLabel}>
            {roleSubtitle ? `${jobTitle} ${roleSubtitle}` : ""}
          </small> */}

            <small className={styles.roleLabel}>{roleName ? `${roleName} ${layoutContext?.solutionId}` : ''}</small>
          </span>
        )}
        <UserProfileImage
          width={isMetronic ? 30 : 50}
          height={isMetronic ? 30 : 50}
          onClick={handleUserProfileImageClick}
          fromLmsBanner={isMetronic}
        />
      </div>
      <ConditionalDisplay condition={showProfileModal}>
        <ProfileModal
          showProfileModal={showProfileModal}
          setShowProfileModal={setShowProfileModal}
          userRole={userRole}
          solutionId={solutionId}
        />
      </ConditionalDisplay>
    </div>
  )
}

const ProfileModal = ({ showProfileModal, setShowProfileModal, userRole, solutionId }) => {
  const { instance, accounts } = useMsal()
  const router = useRouter()

  const handleLogoutClick = async (e) => {
    localStorage.removeItem('teamData')
    // instance.logoutPopup();
    const account = instance.getActiveAccount()
    if (account) {
      await instance.logoutRedirect({
        account,
        postLogoutRedirectUri: '/form-builder-studio'
      })
    }
    setShowProfileModal(false)
  }

  const routeToUserProfile = () => {
    router.push('/MyProfile')
    setShowProfileModal(false)
  }

  const routeToAdminCenter = () => {
    window.open('https://admin.papyrrus.com/')
    setShowProfileModal(false)
  }

  return (
    <ProfileModalContainer showProfileModal={showProfileModal} setShowProfileModal={setShowProfileModal}>
      <ProfileImage solutionId={solutionId} />
      <ProfileInfo />
      <ButtonContainer>
        <div style={{ display: 'flex', gap: '20px', marginBottom: '20px' }}>
          <SecondaryButton style={{ width: userRole === 'Admin' ? '190px' : '400px' }} text="My Profile" onClick={routeToUserProfile} />
          <ConditionalDisplay condition={userRole === 'Admin'}>
            {/* Commenting the onClick function and passing empty fn in dev and qa which was asked by the BA's */}
            <SecondaryButton
              width={190}
              text="Admin Center"
              onClick={() => {}}
              // onClick={routeToAdminCenter}
            />
          </ConditionalDisplay>
        </div>
        <SecondaryButton width={400} text="Sign Out" onClick={handleLogoutClick} />
      </ButtonContainer>
    </ProfileModalContainer>
  )
}

const ProfileModalContainer = ({ children, showProfileModal, setShowProfileModal }) => {
  const handleOutsideClick = () => {
    setShowProfileModal(false)
  }

  const { ref: clickAwayRef } = useClickAwayListener({
    onOutsideClick: handleOutsideClick
  })

  return (
    <div className={styles.profileModalContainer} ref={clickAwayRef}>
      <ConditionalDisplay condition={showProfileModal}>{children}</ConditionalDisplay>
    </div>
  )
}

const ProfileImage = ({ solutionId }) => {
  const router = useRouter()
  return (
    <div className={styles.profileImageContainer}>
      <div className={styles.profileImage}>
        <UserProfileImage width={100} height={100} />
      </div>
    </div>
  )
}

const ProfileInfo = ({}) => {
  const { formattedTimeZone } = useUtilityFunctions()
  const { userPrincipalName } = useContext(MicrosoftEntraIdContext)

  return (
    <div className={styles.profileInfoContainer}>
      <UserProfileDisplayNameLabel />
      <Text text={userPrincipalName} />
      <Text text={formattedTimeZone()} />
    </div>
  )
}

const ButtonContainer = ({ children }) => {
  return <div className={styles.buttonContainer}>{children}</div>
}

const Logo = ({ setPopupModule, dynamicLogo, width, height, featureName, popupModule, features }) => {
  const layoutContext = useContext(LayoutContext)

  const isMetronic = layoutContext.solutionId === 12 || layoutContext.solutionId === 11 || layoutContext.solutionId === 13

  return (
    <div className={styles.logoContainer}>
      <div className={styles.appContainer}>
        <Image
          src={AppMenu}
          alt="App Menu"
          className={styles.appHover}
          onClick={(e) => {
            e.stopPropagation()
            setPopupModule((prev) => !prev)
          }}
        />
      </div>
      {dynamicLogo && (
        <Image
          src={dynamicLogo}
          width={width}
          height={height}
          alt="Image"
          style={!featureName ? { position: 'relative', marginBottom: '0.5rem', height: '56px' } : undefined}
        />
      )}
      <span className={styles.featureHeader}>{featureName ? `${featureName}` : null}</span>

      {isMetronic && (
        <TextInput placeholder={'Global Search'} theme={'metronic'} icon={<i className="pi pi-search" style={{ color: '#5151511a' }} />} />
      )}

      <ConditionalDisplay condition={popupModule}>
        <ClickAwayListener isOpen={popupModule} onOutsideClick={() => setPopupModule(false)}>
          <div className={styles.appLauncherDropdown}>
            <div className={styles.featureLabel}>Apps</div>
            <div className={styles.appLine}></div>
            <div className={styles.featureGrid}>
              {features.map((feature, index) => (
                <Feature
                  key={index}
                  {...feature}
                  backgroundColor={feature.backgroundColor}
                  onClose={() => setPopupModule(false)}
                  solutionId={feature?.solutionId ?? 1}
                />
              ))}
            </div>
          </div>
        </ClickAwayListener>
      </ConditionalDisplay>
    </div>
  )
}

const Feature = ({ name, logo, width, height, path, onClose, backgroundColor, solutionId }) => {
  const router = useRouter()
  const layoutContext = useContext(LayoutContext)

  const handleClick = () => {
    if (path) {
      router.push(path)
      if (layoutContext?.setSolutionId) layoutContext.setSolutionId(solutionId)
    }
    if (onClose) {
      onClose()
    }
  }

  return (
    <div onClick={handleClick} className={styles.featureItem} style={{ backgroundColor }}>
      <Image src={logo} alt={name} width={width} height={height} />
      {name}
    </div>
  )
}

const Text = ({ text }) => {
  return <div className={styles.text}>{text}</div>
}
